@extends('layouts.app')

@section("title","رژیم من")


@php
    $user = auth()->user();

    // اگه رژیم پرداخت نشده باشه، فقط شنبه و یک‌شنبه نمایش بده
    $showLimited = !$user->current_diet_paid;

@endphp

@php

    use App\Models\DietMeta;

    $user = auth()->user();
    $showNextStepButton = false;
    $remainingDays = 0;

    if ($user->diet_start_at && $user->diet_duration_days && $user->current_diet_paid) {
        $dietStartDate = \Carbon\Carbon::parse($user->diet_start_at);
        $dietEndDate = $dietStartDate->copy()->addDays($user->diet_duration_days);

        if (now()->lessThanOrEqualTo($dietEndDate)) {
            $showNextStepButton = false;
            $remainingDays = now()->diffInDays($dietEndDate, false);
        } else {
            $showNextStepButton = true;
            $remainingDays = 0;
        }
    } elseif (!$user->current_diet_paid && $user->diet_duration_days) {
        // اگر پرداخت نشده بود ولی مدت رژیم مشخص بود، کل مدت را نشان بده
        $defaultDuration = 14; // پیش‌فرض برای مرحله اول

        if ($user->activeDietPlan) {
            switch ($user->activeDietPlan->week_number) {
                case 2:
                    $defaultDuration = 30;
                    break;
                case 3:
                    $defaultDuration = 180;
                    break;
            }
        }

        $remainingDays = $defaultDuration;    }

    $remainingDays = intval($remainingDays);
@endphp



@section("contents")

    <div style="background-color:#f5f7fa ;" class="rbt-breadcrumb-default rbt-breadcrumb-style-3">
        <div class="breadcrumb-inner">

        </div>
        <div class="container">
            <div class="row">
                <div class="col-lg-8">
                    <div class="content text-start">
                        <ul class="page-list">
                            <li class="rbt-breadcrumb-item"><a href="{{route("home.index")}}">خانه</a></li>
                            <li>
                                <div class="icon-right"><i class="feather-chevron-left"></i></div>
                            </li>
                            <li class="rbt-breadcrumb-item active">رژیم شما</li>
                        </ul>
                        <h2 class="title">
                            @switch($dietPlan->week_number)
                                @case(1)
                                    رژیم غذایی مرحله اول شما
                                    @break

                                @case(2)
                                    رژیم غذایی مرحله دوم شما
                                    @break

                                @case(3)
                                    رژیم غذایی تثبیت شما
                                    @break

                                @default
                                    رژیم غذایی شما
                            @endswitch
                        </h2>

                        <p class="description">
                            {!! $dietPlan->slogan !!}
                        </p>
                        @if($dietPlan->week_number < 3)
                            <ul class="rbt-meta">
                                <li><i class="feather-calendar"></i> زمان باقی مانده از این مرحله رژیم:
                                    <b>{{ $remainingDays }} روز</b></li>
                            </ul>
                        @endif

                        <a href="{{route("logout.delete.user")}}" class="btn ra-btn btn-danger mt--30">حذف و ریست این
                            اکانت</a>

                        @if($showNextStepButton)
                            <div class="alert alert-success mt--30 radius-10">
                                این مرحله از رژیم شما به پایان رسیده است. با کلیک بر روی دکمه زیر می توانید به مرحله
                                بعدی رژیم بروید.
                                <p></p>
                                <a href="{{route("profile.next-stage")}}"
                                   class="rbt-btn btn-md btn-gradient hover-icon-reverse float-end"
                                   style="margin-top: -15px;">
                                    رفتن به مرحله بعد
                                </a>
                            </div>
                        @endif

                        @if(session('success'))
                            <div class="alert alert-success" style="white-space: pre-line;">
                                {!! nl2br(e(session('success'))) !!}
                            </div>
                        @endif

                        @if(session('error'))
                            <div class="alert alert-danger">
                                {{ session('error') }}
                            </div>
                        @endif

                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="rbt-course-details-area ptb--60">
        <div class="container">
            <div class="row g-5">

                <div style="margin-top: -30px;" class="col-lg-8 order-2 order-lg-1">
                    <div class="course-details-content">
                        <div class="course-details-content">
                            <div class="rbt-inner-onepage-navigation sticky-top">
                                <nav class="mainmenu-nav onepagenav">
                                    <ul class="mainmenu">
                                        <li class="current">
                                            <a href="#overview">توضیحات رژیم</a>
                                        </li>
                                        @if($dietPlan->week_number < 3)
                                            <li>
                                                <a href="#coursecontent">برنامه غذایی هفتگی</a>
                                            </li>
                                        @endif
                                        <li>
                                            <a href="#details">نکات مهم</a>
                                        </li>
                                        <li>
                                            <a href="#intructor">نظر دکتر</a>
                                        </li>
                                        <li>
                                            <a href="#review">مسیر طی شده</a>
                                        </li>
                                    </ul>
                                </nav>
                            </div>


                            @if(!$showLimited)
                                <div class="rbt-course-feature-box overview-wrapper rbt-shadow-box mt--30 has-show-more"
                                    id="overview">
                                    <div class="rbt-course-feature-inner has-show-more-inner-content">
                                        <div class="section-title">
                                            <h4 class="rbt-title-style-3">آگاهانه رژیم بگیرید</h4>
                                        </div>
                                        <p>🍃 <strong>لذت‌های زودگذر غم‌های طولانی بر پی دارد و کلید سعادت، رهایی از
                                                دلبستگی‌هاست.</strong></p>

                                        <p>📌 مسیر کاهش وزن شما کاملاً شخصی‌سازی شده و بر اساس اطلاعات جسمانی و فردی شما
                                            تنظیم شده است.</p>

                                        <ul>
                                            <li>🌬 در صورت رعایت کامل رژیم، ممکن است بوی تنفس شبیه افراد روزه‌دار شود که
                                                طبیعی است و با افزایش مصرف آب کاهش می‌یابد.
                                            </li>
                                            <li>💊 مصرف روزانه یک عدد <strong>کپسول مولتی‌ویتامین مینرال</strong> بعد از
                                                صبحانه توصیه می‌شود.
                                            </li>
                                            <li>🚶‍♂️ برای افزایش سرعت کاهش وزن، <strong>روزانه یک ساعت پیاده‌روی</strong> یا
                                                ورزش‌های هوازی پیشنهاد می‌شود. ضربان قلب در حین ورزش باید به ۱۱۰ ضربه در
                                                دقیقه برسد.
                                            </li>
                                            <li>🏋️‍♀️ <strong>ورزش‌های بدنسازی</strong> در طول رژیم مجاز نیستند، چون موجب
                                                عضله‌سازی و جلوگیری از کاهش وزن می‌شوند.
                                            </li>
                                            <li>🍽 <strong>خرده‌خواری و ریزه‌خواری</strong> باعث کبد چرب می‌شود. فقط زمانی
                                                غذا بخورید که واقعاً گرسنه‌اید.
                                            </li>
                                            <li>💧 برای سلامت بیشتر، <strong>نیم‌ساعت قبل و بعد از غذا آب ننوشید.</strong>
                                            </li>
                                            <li>😩 در صورت احساس ضعف، یک قاشق عسل + یک قاشق سرکه سیب را در یک لیوان آب ولرم
                                                حل کرده و میل کنید.
                                            </li>
                                            <li>🔄 جابجایی وعده‌های غذایی کاملاً مجاز است.</li>
                                            <li>📝 اگر غذایی در لیست را نمی‌پسندید، می‌توانید آن را با غذای مشابه از همان
                                                لیست جایگزین کنید.
                                            </li>
                                            <li>🍽 <strong>هر دو هفته یک روز تغذیه آزاد (Cheat Day)</strong> دارید. در این
                                                روز هر چیزی جز شیرینی‌جات مجاز است. مصرف شیرینی‌جات می‌تواند تا ۵ روز روند
                                                کاهش وزن را متوقف کند.
                                            </li>
                                            <li>🥪 میان‌وعده‌ها فقط در صورت گرسنگی خورده شوند، در غیر این صورت از آن صرف نظر
                                                شود.
                                            </li>
                                            <li>💡 اگر فقط یک مرحله رژیم را به‌درستی اجرا کنید، خواهید دید که در گذشته اشتباه
                                                غذا می‌خوردید.
                                            </li>
                                            <li>⏳ فراموش نکنید: <strong>تا گرسنه نشدید غذا نخورید و قبل از سیر شدن کامل از
                                                    غذا خوردن دست بکشید.</strong></li>
                                            <li>🍵 چای نباید با قند، نبات، کشمش یا هر شیرینی دیگری خورده شود؛ در نهایت با
                                                مقدار کمی مویز.
                                            </li>
                                            <li>🧂 مصرف غذای شور می‌تواند باعث کاهش وزن شود اما نباید بیش از حد شور باشد.
                                            </li>
                                        </ul>

                                        <hr>

                                        <h5 class="mt-4">🚫 غذاهای ممنوعه:</h5>
                                        <ul class="">
                                            <li>بیسکوییت و کیک، شیرینی، قند، شکر، نوشابه (حتی رژیمی) و هر چیز شیرینی</li>
                                            <li>نان، برنج، ماکارونی، سیب‌زمینی، سویا، چیپس، پفک، حبوبات، بیشتر میوه‌ها (جز
                                                خیار، هویج، کیوی، سیب ترش، ذغال‌اخته، تمشک، گریپ‌فروت)
                                            </li>
                                            <li>سوسیس، کالباس، الکل، خرما، ساقه طلایی، غذاهای بسیار شور، شیر، ماست،
                                                لواشک‌های شیرین، آجیل
                                            </li>
                                        </ul>
                                        <p class="text-danger"><strong>⚠️ حتی یک وعده مصرف از این غذاها ممکن است زحمات چند
                                                روز شما را از بین ببرد.</strong></p>

                                        <h5 class="mt-4">✅ غذاهای مجاز:</h5>
                                        <ul class="">

                                            <li>پروتئین‌ها: گوشت گاو، گوسفند، مرغ، ماهی، جگر (آبپز، کبابی یا سرخ‌شده)</li>
                                            <li>سبزیجات و میوه‌های مجاز: خیار، گوجه، کیوی، کاهو، کلم، هویج خام، شلغم، قارچ،
                                                کدو سبز، بادمجان، اسفناج، کرفس، گریپ‌فروت، سیب ترش، ذغال‌اخته
                                            </li>
                                            <li>تخم‌مرغ، پنیر، زیتون بدون شوری</li>

                                        </ul>


                                    </div>
                                    <div class="rbt-show-more-btn">اطلاعات بیشتر</div>
                                </div>
                            

                            @if($dietPlan->week_number == 2 && ($note = \App\Models\DietMeta::seasonalFruitNoteFor($user->blood_group)))
                                <div class="rbt-course-feature-box overview-wrapper rbt-shadow-box mt--30 has-show-more"
                                     id="season">
                                    <div class="rbt-course-feature-inner has-show-more-inner-content">
                                        <div class="section-title">
                                            <h4 class="rbt-title-style-3">توجه فصلی</h4>
                                        </div>
                                        <div class="alert alert-info mt-3">
                                            {{ $note }}
                                        </div>
                                    </div>
                                </div>
                            @endif

                            @if($user->thyroid)
                                {{-- اگر کاربر مشکل تیروئید دارد --}}

                                <div class="rbt-course-feature-box rbt-shadow-box details-wrapper mt--30"
                                     id="thyroid-warning">
                                    <div class="row g-5">
                                        <div class="col-lg-12">
                                            <div class="section-title">
                                                <h4 class="rbt-title-style-3 mb--20">نکات مهم برای افراد مبتلا به
                                                    تیروئید</h4>
                                            </div>
                                            <ul class="">
                                                <p>
                                                    در صورتی که مبتلا به اختلالات تیروئیدی هستید (کم‌کاری یا پرکاری
                                                    تیروئید)، رعایت برخی نکات در دوران رژیم بسیار ضروری است.
                                                    به عنوان مثال، از مصرف مواد غذایی مانند <strong>کلم، گل‌کلم، بروکلی،
                                                        سویا و شلغم</strong> تا حد امکان پرهیز کنید؛ چرا که این مواد
                                                    ممکن است در عملکرد تیروئید اختلال ایجاد کنند.
                                                    همچنین مصرف داروهای تیروئید را طبق تجویز پزشک و با فاصله زمانی مناسب
                                                    از وعده‌های غذایی (خصوصاً لبنیات و مکمل‌های آهن و کلسیم) انجام دهید.
                                                    مهم‌تر از همه، روند کاهش وزن در افراد دارای تیروئید ممکن است کندتر
                                                    باشد، اما با پیوستگی و رعایت رژیم، شما نیز به نتیجه مطلوب خواهید
                                                    رسید.
                                                </p>
                                            </ul>
                                        </div>
                                    </div>
                                </div>

                            @endif

                            @if($user->diabetes)
                                {{-- اگر کاربر دیابت دارد --}}

                                <div class="rbt-course-feature-box rbt-shadow-box details-wrapper mt--30"
                                     id="diabetes-warning">
                                    <div class="row g-5">
                                        <div class="col-lg-12">
                                            <div class="section-title">
                                                <h4 class="rbt-title-style-3 mb--20">نکات مهم برای افراد مبتلا به
                                                    دیابت</h4>
                                            </div>
                                            <ul class="">
                                                <p>
                                                    اگر شما مبتلا به دیابت هستید، <strong class="text-danger">حتماً پیش
                                                        از شروع رژیم، با
                                                        پشتیبانی تماس بگیرید</strong> تا توضیحات تکمیلی و متناسب با
                                                    شرایط خاص شما ارائه شود.
                                                    رژیم‌های غذایی برای افراد دیابتی نیازمند دقت بیشتر و هماهنگی با مصرف
                                                    دارو یا انسولین هستند.
                                                    در طول رژیم باید از مصرف غذاهایی با شاخص گلایسمی بالا مانند <strong>برنج
                                                        سفید، قند، شکر، نوشابه‌ها، نان‌های سفید و شیرینی‌ها</strong> تا
                                                    حد زیادی پرهیز شود.
                                                    بهتر است وعده‌های غذایی شما منظم باشد و از حذف وعده‌ها خودداری کنید
                                                    تا قند خون شما دچار نوسان نشود.
                                                    همچنین توصیه می‌شود سطح قند خون خود را به‌طور منظم بررسی کنید و در
                                                    صورت احساس ضعف یا سرگیجه با پزشک خود مشورت نمایید.
                                                </p>
                                            </ul>
                                        </div>
                                    </div>
                                </div>

                            @endif

                            

                            

                            @if($dietPlan->week_number < 3)
                                <div class="course-content rbt-shadow-box coursecontent-wrapper mt--30"
                                     id="coursecontent">
                                    <div class="rbt-course-feature-inner">
                                        <div class="section-title">
                                            <h4 class="rbt-title-style-3">رژیم اختصاصی
                                                {{auth()->user()->name ?? "شما"}}</h4>
                                        </div>
                                        <div class="row">
                                            <div class="col-12">
                                                @php
                                                    $user = auth()->user();

                                                    // اگه رژیم پرداخت نشده باشه، فقط شنبه و یک‌شنبه نمایش بده
                                                    $showLimited = !$user->current_diet_paid;

                                                    $days = [
                                                        'saturday'  => 'شنبه',
                                                        'sunday'    => 'یک‌شنبه',
                                                        'monday'    => 'دوشنبه',
                                                        'tuesday'   => 'سه‌شنبه',
                                                        'wednesday' => 'چهارشنبه',
                                                        'thursday'  => 'پنج‌شنبه',
                                                        'friday'    => 'جمعه',
                                                    ];

                                                    $daysToShow = $showLimited
                                                        ? ['saturday' => 'شنبه', 'sunday' => 'یک‌شنبه']
                                                        : $days;
                                                @endphp


                                                <div
                                                    @if($showLimited) class="fade-row" @endif>
                                                    <div class="d-md-none">
                                                    @foreach($daysToShow as $dayKey => $dayName)
                                                        <div class="rbt-card-box rbt-hover mb-5">
                                                            <div class="rbt-card-body">
                                                                <h5 class="rbt-card-title mb-3 border-bottom pb-2">{{ $dayName }}</h5>
                                                                
                                                                <div class="rbt-card-text">
                                                                    <div class="mb-3">
                                                                        <strong>صبحانه:</strong>
                                                                        <p class="mb-0">
                                                                            {{ isset($weeklyPlan[$dayKey]['breakfast'])
                                                                                ? DietMeta::adjustSeasonalFruitsForMeal($weeklyPlan[$dayKey]['breakfast'], $user->blood_group)
                                                                                : 'نامشخص' }}
                                                                        </p>
                                                                    </div>
                                                                    
                                                                    <div class="mb-3">
                                                                        <strong>ناهار:</strong>
                                                                        <p class="mb-0">
                                                                            {{ isset($weeklyPlan[$dayKey]['lunch'])
                                                                                ? DietMeta::adjustSeasonalFruitsForMeal($weeklyPlan[$dayKey]['lunch'], $user->blood_group)
                                                                                : 'نامشخص' }}
                                                                        </p>
                                                                    </div>
                                                                    
                                                                    <div class="mb-3">
                                                                        <strong>شام:</strong>
                                                                        <p class="mb-0">
                                                                            {{ isset($weeklyPlan[$dayKey]['dinner'])
                                                                                ? DietMeta::adjustSeasonalFruitsForMeal($weeklyPlan[$dayKey]['dinner'], $user->blood_group)
                                                                                : 'نامشخص' }}
                                                                        </p>
                                                                    </div>
                                                                    
                                                                    <div>
                                                                        <strong>میان‌وعده:</strong>
                                                                        <p class="mb-0">
                                                                            {{ isset($weeklyPlan[$dayKey]['snack'])
                                                                                ? DietMeta::adjustSeasonalFruitsForMeal($weeklyPlan[$dayKey]['snack'], $user->blood_group)
                                                                                : 'نامشخص' }}
                                                                        </p>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    @endforeach
                                                </div>
                                                    <table class="table d-none d-md-block">
                                                        <thead>
                                                        <tr>
                                                            <th>روز</th>
                                                            <th>صبحانه</th>
                                                            <th>ناهار</th>
                                                            <th>شام</th>
                                                            <th>میان‌وعده</th>
                                                        </tr>
                                                        </thead>
                                                        <tbody>
                                                        @foreach($daysToShow as $dayKey => $dayName)
                                                            <tr>
                                                                <td>{{ $dayName }}</td>
                                                                <td>
                                                                    {{ isset($weeklyPlan[$dayKey]['breakfast'])
                                                                        ? DietMeta::adjustSeasonalFruitsForMeal($weeklyPlan[$dayKey]['breakfast'], $user->blood_group)
                                                                        : 'نامشخص' }}
                                                                </td>
                                                                <td>
                                                                    {{ isset($weeklyPlan[$dayKey]['lunch'])
                                                                        ? DietMeta::adjustSeasonalFruitsForMeal($weeklyPlan[$dayKey]['lunch'], $user->blood_group)
                                                                        : 'نامشخص' }}
                                                                </td>
                                                                <td>
                                                                    {{ isset($weeklyPlan[$dayKey]['dinner'])
                                                                        ? DietMeta::adjustSeasonalFruitsForMeal($weeklyPlan[$dayKey]['dinner'], $user->blood_group)
                                                                        : 'نامشخص' }}
                                                                </td>
                                                                <td>
                                                                    {{ isset($weeklyPlan[$dayKey]['snack'])
                                                                        ? DietMeta::adjustSeasonalFruitsForMeal($weeklyPlan[$dayKey]['snack'], $user->blood_group)
                                                                        : 'نامشخص' }}
                                                                </td>
                                                            </tr>
                                                        @endforeach
                                                        </tbody>
                                                    </table>
                                                </div>                                               
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            @endif
                            @endif
                            {{-- اگر کاربر در حالت نمایش محدود است، پیام و دکمه پرداخت را نشان بده --}}
                            @if($showLimited)
                                                    <div class="alert alert-warning mt-4 text-center">
                                                        <p>برای مشاهده کامل اطلاعات
                                                            <b>
                                                        @switch($dietPlan->week_number)
                                @case(1)
                                    رژیم مرحله اول
                                    @break

                                @case(2)
                                    رژیم مرحله دوم
                                    @break

                                @case(3)
                                    رژیم تثبیت
                                    @break

                                @default
                                    رژیم
                            @endswitch
                            </b>
                                                        ، لطفاً با پرداخت هزینه این
                                                            مرحله ثبت‌نام خود را تکمیل کنید.</p>
                                                        <a href="{{route("payment.pay")}}"
                                                           class="btn btn-primary ra-btn">پرداخت {{$amount = \App\Models\Setting::get('diet_price', 199000)/1000}}
                                                            هزارتومان</a>
                                                    </div>
                                                @endif



                            @if(!$showLimited)                    
                                @if($user->gender === 'female')

                                    <div class="rbt-course-feature-box rbt-shadow-box details-wrapper mt--30" id="woman">
                                        <div class="row g-5">

                                            <div class="col-lg-12">
                                                <div class="section-title">
                                                    <h4 class="rbt-title-style-3 mb--20">یک نکته مهم برای خانم های
                                                        محترم... </h4>
                                                </div>
                                                <ul class="">
                                                    <p>
                                                        در طول دوران رژیم، ممکن است به دلیل تغییرات هورمونی و سیکل قاعدگی،
                                                        دچار
                                                        توقف در کاهش وزن شوید یا حتی افزایش جزئی وزن را تجربه کنید.
                                                        این یک پدیده کاملاً طبیعی است و به معنای بی‌اثر بودن رژیم نیست.
                                                        لطفاً به برنامه خود اعتماد داشته باشید و رژیم را بدون تغییر ادامه
                                                        دهید،
                                                        چرا که پس از این دوره، روند کاهش وزن ادامه خواهد یافت.
                                                    </p>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>

                                @endif
                                <div class="rbt-course-feature-box rbt-shadow-box details-wrapper mt--30" id="details">
                                    <div class="row g-5">

                                        <div class="col-lg-12">
                                            <div class="section-title">
                                                <h4 class="rbt-title-style-3 mb--20">نکات مهم رژیم شما</h4>
                                            </div>
                                            {!! \App\Models\DietMeta::adjustSeasonalFruitsForMeal($dietPlan->description, $user->blood_group) !!}
                                        </div>
                                    </div>
                                </div>
                            @endif
                            <div class="about-author-list rbt-shadow-box featured-wrapper mt--30 has-show-more"
                                 id="intructor">
                                <div class="section-title">
                                    <h4 class="rbt-title-style-3">نظرات دکتر نائیجی بر روند رژیم شما</h4>
                                </div>

                                <div class="has-show-more-inner-content rbt-featured-review-list-wrapper">
                                    @forelse($doctorComments as $comment)
                                        <div class="rbt-course-review about-author">
                                            <div class="media">
                                                <div class="thumbnail">
                                                    <a href="#">
                                                        <img src="{{ asset('images/testimonial/testimonial-3.jpg') }}"
                                                             alt="Author Image">
                                                    </a>
                                                </div>
                                                <div class="media-body">
                                                    <div class="author-info">
                                                        <h5 class="title">
                                                            <a class="hover-flip-item-wrapper" href="#">
                                                                دکتر نائیجی
                                                            </a>
                                                        </h5>
                                                        <li>{{ \Morilog\Jalali\Jalalian::fromCarbon($comment->created_at)->format('%Y/%m/%d') }}</li>
                                                    </div>
                                                    <div class="content">
                                                        <p class="description">{{ $comment->content }}</p>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    @empty
                                        <div class="rbt-course-review about-author">
                                            <div class="content">
                                                <p class="description text-muted">فعلاً نظری ثبت نشده است.</p>
                                            </div>
                                        </div>
                                    @endforelse
                                </div>
                                <div class="rbt-show-more-btn">مشاهده بیشتر</div>
                            </div>

                            <div class="about-author-list rbt-shadow-box mt--30"
                                 id="review">
                                <div class="section-title">
                                    <h4 class="rbt-title-style-3">روند پیشرفت شما</h4>
                                </div>
                                <div class="has-show-more-inner-content rbt-featured-review-list-wrapper">


                                    @php
                                        $todayKey = 'weight_' . \Carbon\Carbon::now()->format('Y-m-d');
                                        $exists = \App\Models\UserMeta::where('user_id', auth()->id())
                                            ->where('key', $todayKey)
                                            ->exists();
                                    @endphp

                                    @if(!$exists)
                                        <p>
                                            برای رسیدن به وضعیت مطلوب و آگاهی دکتر نسبت به وضعیت شما به طور روزانه، وزن
                                            خودتان را در کادر زیر وارد کنید:
                                        </p>
                                        <form action="{{ route('profile.weight.store') }}" method="POST"
                                              class="newsletter-form-1 mt--40">
                                            @csrf
                                            <input type="text" name="weight" placeholder="وزن امروز خود را وارد کنید"
                                                   class="shadow shadow-4 rbt-shadow-box">
                                            <button type="submit"
                                                    class="rbt-btn btn-md btn-gradient hover-icon-reverse">
                                                <span class="icon-reverse-wrapper">
                                                    <span class="btn-text">ثبت</span>
                                                    <span class="btn-icon"><i class="feather-arrow-left"></i></span>
                                                    <span class="btn-icon"><i class="feather-arrow-left"></i></span>
                                                </span>
                                            </button>
                                        </form>
                                    @else
                                        <p>شما امروز وزن خود را ثبت کرده‌اید.</p>
                                    @endif

                                    @if($dietPlan->week_number < 3)
                                        @if($weightGoal)
                                            <p>🎯 وزن هدف این دوره: <strong>{{ $weightGoal }} کیلوگرم</strong></p>
                                        @endif
                                    @endif

                                    <div class="mt--30">
                                        <div class="chart-container"
                                             style="width: 100%; overflow-x: auto; height: 300px;">
                                            <canvas id="weightChart" style="max-width: 100%; height: auto;"></canvas>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4">
                    <div class="course-sidebar sticky-top rbt-shadow-box course-sidebar-top rbt-gradient-border">
                        <div class="inner">
                            <a class="video-popup-with-text video-popup-wrapper text-center popup-video mb--15"
                               href="{{route('aparat.video2')}}">
                                <div class="video-content">
                                    <img class="w-100 rbt-radius" src="{{asset("images")}}/others/video-01.jpg"
                                         alt="تصاویر ویدئو">
                                    <div class="position-to-top">
                                        <span class="rbt-btn rounded-player-2 with-animation">
                                            <span class="play-icon"></span>
                                        </span>
                                    </div>
                                    <span class="play-view-text d-block color-white">
                                        <i class="feather-eye"></i> توضیحات رژیم شما </span>
                                </div>
                            </a>


                            <div class="content-item-content">

                                <span class="subtitle">
                                    قبل از شروع رژیم حتما این ویدئو مشاهده شود
                                </span>

                                @php
                                    // محاسبه BMI شروع دوره
                                    $userHeight = auth()->user()->height; // قد به سانتی‌متر
                                    $heightInMeters = $userHeight / 100; // تبدیل به متر
                                    $startBMI = $startingWeight ? round($startingWeight / ($heightInMeters * $heightInMeters), 1) : null;
                                    
                                    // دریافت آخرین وزن ثبت شده
                                    $latestWeight = null;
                                    $weightMetas = \App\Models\UserMeta::where('user_id', auth()->id())
                                        ->where('key', 'like', 'weight_%')
                                        ->orderBy('key', 'desc')
                                        ->first();
                                    
                                    if ($weightMetas) {
                                        $latestWeight = (float) $weightMetas->value;
                                    }
                                    
                                    // محاسبه BMI لحظه‌ای
                                    $currentBMI = $latestWeight ? round($latestWeight / ($heightInMeters * $heightInMeters), 1) : null;
                                @endphp

                                <div class="rbt-widget-details has-show-more">
                                    <ul class="has-show-more-inner-content rbt-course-details-list-wrapper">
                                        <li>
                                            <span>مرحله رژیم:</span>
                                            <span
                                                class="rbt-feature-value rbt-badge-5">{{ $dietPlan->name ?? '---' }}</span>
                                        </li>
                                        <li>
                                            <span>تاریخ شروع رژیم:</span>
                                            <span class="rbt-feature-value rbt-badge-5">{{ $startDate }}</span>
                                        </li>
                                        <li>
                                            <span>تاریخ پایان رژیم:</span>
                                            <span class="rbt-feature-value rbt-badge-5">{{ $endDate }}</span>
                                        </li>
                                        <li>
                                            <span>وزن ابتدای دوره:</span>
                                            <span
                                                class="rbt-feature-value rbt-badge-5">{{ $startingWeight }} کیلوگرم</span>
                                        </li>
                                        <li>
                                            <span>قد:</span>
                                            <span
                                                class="rbt-feature-value rbt-badge-5">{{ auth()->user()->height }} cm</span>
                                        </li>
                                        <li>
                                            <span>BMI شروع دوره:</span>
                                            <span class="rbt-feature-value rbt-badge-5">
                                                {{ $startBMI ? $startBMI : '---' }}
                                            </span>
                                        </li>
                                        <li>
                                            <span>BMI لحظه‌ای:</span>
                                            <span class="rbt-feature-value rbt-badge-5">
                                                {{ $currentBMI ? $currentBMI : '---' }}
                                            </span>
                                        </li>
                                        <li>
                                            <span>وضعیت شما:</span>
                                            <span class="rbt-feature-value rbt-badge-5">{{ $status }}</span>
                                        </li>
                                    </ul>

                                    <div class="rbt-show-more-btn">مشاهده بیشتر</div>
                                </div>

                                <div class="social-share-wrapper mt--30 text-center rounded-3xl">
                                    <div
                                        class="rbt-post-share d-flex align-items-center justify-content-center">
                                        <ul class="social-icon social-default transparent-with-border justify-content-center">
                                            <li><a href="https://www.instagram.com/">
                                                    <i class="feather-instagram"></i>
                                                </a>
                                            </li>
                                            <li><a href="https://www.telegram.com/">
                                                    <i class="feather-send"></i>
                                                </a>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="rbt-separator-mid">
        <div class="container">
            <hr class="rbt-separator m-0">
        </div>
    </div>

    <div class="rbt-related-course-area bg-color-white rbt-section-gap">
        <div class="container">
            <div class="section-title mb--30">
                <span class="subtitle bg-primary-opacity">مقاله ها</span>
                <h4 class="title">مقاله های مرتبط با رژیم شما</h4>
            </div>
            <div class="row g-5">

                @foreach($relatedBlogs as $blog)
                    <div class="col-lg-4 col-md-6 col-sm-6 col-12">
                        <div class="rbt-card variation-01 rbt-hover">
                            <div class="rbt-card-img" style="aspect-ratio: 5/4;">
                                <a href="{{ route('blog.show', $blog->slug) }}">
                                    <img src="{{ custom_asset('images/blog/' . ($blog->featured_image ?? 'default.jpg')) }}"
                                         alt="{{ $blog->title }}"
                                         style="aspect-ratio: 5/4; width: 100%; height: 100%; object-fit: cover;">
                                </a>
                            </div>
                            <div class="rbt-card-body">
                                <h4 class="rbt-card-title">
                                    <a href="{{ route('blog.show', $blog->slug) }}">{{ $blog->title }}</a>
                                </h4>

                                @if($blog->excerpt)
                                    <p class="rbt-card-text">{{ Str::limit(strip_tags($blog->excerpt), 100, '...') }}</p>
                                @endif

                                <div class="rbt-author-meta mb--10">
                                    <div class="rbt-author-info">
                                        {{ $blog->author_name ?? 'تیم پشتیبانی' }}
                                    </div>
                                </div>

                            </div>
                        </div>
                    </div>
                @endforeach


            </div>
        </div>
    </div>
@endsection

@section('scripts')
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        fetch("{{ route('weight.chart.data') }}")
            .then(res => res.json())
            .then(chartData => {
                const labels = chartData.map(item => item.date);
                const data = chartData.map(item => item.weight);

                // پیدا کردن مین و مکس واقعی وزن‌ها (فقط عددهای غیر null)
                const validData = data.filter(w => w !== null);
                const minWeight = Math.min(...validData);
                const maxWeight = Math.max(...validData);

                // تنظیم بازه سفارشی
                const yMin = Math.floor(minWeight - 3); // یا -10 یا -20 به دلخواه
                const yMax = Math.ceil(maxWeight + 3);

                Chart.defaults.font.family = 'dana';
                Chart.defaults.font.size = 14;

                new Chart(document.getElementById('weightChart'), {
                    type: 'line',
                    data: {
                        labels: labels,
                        datasets: [{
                            label: 'وزن (کیلوگرم)',
                            data: data,
                            borderColor: '#C06E92',
                            fill: false,
                            spanGaps: true,
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            x: {
                                title: {
                                    display: true,
                                    text: 'تاریخ'
                                }
                            },
                            y: {
                                min: yMin,
                                max: yMax,
                                title: {
                                    display: true,
                                    text: 'وزن'
                                }
                            }
                        }
                    }
                });
            })
            .catch(err => console.error(err));

    </script>

@endsection
