@extends("layouts.app")

@section('title','در حال ساخت رژیم شما')

@section("contents")
    <div class="rbt-elements-area bg-color-white rbt-section-gap">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <div class="text-center" style="background-color: white; border-radius: 30px; box-shadow: var(--shadow-1); padding: 60px 40px;">
                        
                        <!-- آیکون و عنوان اصلی -->
                        <div class="mb-5">
                            <div class="creating-icon mb-4">
                                <i class="feather-settings" style="font-size: 4rem; color: var(--color-primary); animation: rotate 2s linear infinite;"></i>
                            </div>
                            <h2 class="title mb-3" style="color: var(--color-heading);">در حال ساخت رژیم شما</h2>
                            <p class="description" style="color: var(--color-body); font-size: 1.1rem;">
                                لطفاً صبر کنید، رژیم غذایی مخصوص شما بر اساس اطلاعات وارد شده در حال تهیه است...
                            </p>
                        </div>

                        <!-- نوار پیشرفت -->
                        <div class="progress-container mb-5">
                            <div class="progress" style="height: 8px; border-radius: 10px; background-color: #f8f9fa;">
                                <div class="progress-bar" id="progressBar" 
                                     style="width: 0%; background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-secondary) 100%); 
                                            border-radius: 10px; transition: width 0.3s ease;">
                                </div>
                            </div>
                            <div class="progress-percentage mt-2">
                                <span id="progressText" style="font-weight: 600; color: var(--color-primary);">0%</span>
                            </div>
                        </div>

                        <!-- مراحل ساخت رژیم -->
                        <div class="diet-creation-steps">
                            <div class="step" id="step1">
                                <div class="step-icon">
                                    <i class="feather-user-check"></i>
                                </div>
                                <div class="step-text">
                                    <h6>تحلیل اطلاعات شخصی</h6>
                                    <small>بررسی قد، وزن و مشخصات فردی</small>
                                </div>
                            </div>

                            <div class="step" id="step2">
                                <div class="step-icon">
                                    <i class="feather-activity"></i>
                                </div>
                                <div class="step-text">
                                    <h6>تشخیص طبع بدن</h6>
                                    <small>تحلیل طبع گرمایی، سرمایی یا معتدل</small>
                                </div>
                            </div>

                            <div class="step" id="step3">
                                <div class="step-icon">
                                    <i class="feather-heart"></i>
                                </div>
                                <div class="step-text">
                                    <h6>بررسی وضعیت سلامت</h6>
                                    <small>در نظر گیری دیابت و تیروئید</small>
                                </div>
                            </div>

                            <div class="step" id="step4">
                                <div class="step-icon">
                                    <i class="feather-clipboard"></i>
                                </div>
                                <div class="step-text">
                                    <h6>انتخاب رژیم مناسب</h6>
                                    <small>تطبیق با گروه خونی و نیازها</small>
                                </div>
                            </div>

                            <div class="step" id="step5">
                                <div class="step-icon">
                                    <i class="feather-check-circle"></i>
                                </div>
                                <div class="step-text">
                                    <h6>نهایی سازی رژیم</h6>
                                    <small>آماده سازی برنامه غذایی شما</small>
                                </div>
                            </div>
                        </div>

                        <!-- پیام انتظار -->
                        <div class="waiting-message mt-4" id="waitingMessage">
                            <p style="color: var(--color-body); font-size: 0.95rem;">
                                <i class="feather-clock" style="color: var(--color-primary);"></i>
                                این فرآیند معمولاً کمتر از یک دقیقه طول می‌کشد
                            </p>
                        </div>

                        <!-- دکمه ادامه (مخفی در ابتدا) -->
                        <div class="continue-button mt-4" id="continueButton" style="display: none;">
                            <div class="success-message mb-3">
                                <i class="feather-check-circle" style="color: #28a745; font-size: 2rem;"></i>
                                <h5 style="color: #28a745; margin: 10px 0;">رژیم شما آماده است!</h5>
                                <p style="color: var(--color-body); font-size: 1.3rem;">
                                    رژیم غذایی مخصوص شما با موفقیت تهیه شد
                                </p>
                            </div>
                            <a href="{{ route('profile.my-diet') }}" class="rbt-btn btn-md btn-gradient hover-icon-reverse">
                                <span class="icon-reverse-wrapper">
                                    <span class="btn-text">مشاهده رژیم من</span>
                                    <span class="btn-icon"><i class="feather-arrow-left"></i></span>
                                    <span class="btn-icon"><i class="feather-arrow-left"></i></span>
                                </span>
                            </a>
                        </div>

                    </div>
                </div>
            </div>
        </div>
    </div>

    <style>
        @keyframes rotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        .diet-creation-steps {
            display: flex;
            flex-direction: column;
            gap: 20px;
            max-width: 400px;
            margin: 0 auto;
        }

        .step {
            display: flex;
            align-items: center;
            padding: 15px 20px;
            border-radius: 12px;
            background-color: #f8f9fa;
            opacity: 0.5;
            transition: all 0.3s ease;
            border-left: 4px solid transparent;
        }

        .step.active {
            opacity: 1;
            background-color: rgba(var(--color-primary-rgb), 0.1);
            border-left-color: var(--color-primary);
            transform: translateX(5px);
        }

        .step.completed {
            opacity: 1;
            background-color: rgba(40, 167, 69, 0.1);
            border-left-color: #28a745;
        }

        .step-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: #e9ecef;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-left: 15px;
            transition: all 0.3s ease;
        }

        .step.active .step-icon {
            background-color: var(--color-primary);
            color: white;
        }

        .step.completed .step-icon {
            background-color: #28a745;
            color: white;
        }

        .step-text {
            text-align: right;
            flex: 1;
        }

        .step-text h6 {
            margin: 0 0 5px 0;
            font-size: 2rem;
            font-weight: 600;
        }

        .step-text small {
            color: var(--color-body);
            font-size: 1.2rem;
        }

        .continue-button {
            text-align: center;
        }

        .success-message {
            animation: successPulse 2s ease-in-out infinite;
        }

        @keyframes successPulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .continue-button .rbt-btn {
            font-size: 1.1rem;
            box-shadow: 0 4px 15px rgba(var(--color-primary-rgb), 0.3);
        }

        .continue-button .rbt-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(var(--color-primary-rgb), 0.4);
        }

        @media (max-width: 768px) {
            .diet-creation-steps {
                max-width: 100%;
            }

            .step {
                padding: 12px 15px;
            }

            .step-icon {
                width: 35px;
                height: 35px;
                margin-left: 10px;
            }
        }
    </style>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const steps = [
                { id: 'step1', duration: 1000 },
                { id: 'step2', duration: 1200 },
                { id: 'step3', duration: 1000 },
                { id: 'step4', duration: 1300 },
                { id: 'step5', duration: 1500 }
            ];

            let currentStep = 0;
            let progress = 0;
            const totalDuration = 6000; // 6 ثانیه
            const progressBar = document.getElementById('progressBar');
            const progressText = document.getElementById('progressText');

            // شروع پیشرفت
            function updateProgress() {
                progress += 1;
                const percentage = Math.min(progress, 100);
                
                progressBar.style.width = percentage + '%';
                progressText.textContent = percentage + '%';

                if (progress < 100) {
                    setTimeout(updateProgress, totalDuration / 100);
                }
            }

            // شروع مراحل
            function activateStep(stepIndex) {
                if (stepIndex > 0) {
                    document.getElementById(steps[stepIndex - 1].id).classList.remove('active');
                    document.getElementById(steps[stepIndex - 1].id).classList.add('completed');
                }
                
                if (stepIndex < steps.length) {
                    document.getElementById(steps[stepIndex].id).classList.add('active');
                    
                    setTimeout(() => {
                        activateStep(stepIndex + 1);
                    }, steps[stepIndex].duration);
                } else {
                    // همه مراحل تمام شد، نمایش دکمه ادامه
                    setTimeout(() => {
                        showContinueButton();
                    }, 500);
                }
            }

            // نمایش دکمه ادامه
            function showContinueButton() {
                // مخفی کردن پیام انتظار
                document.getElementById('waitingMessage').style.display = 'none';

                // نمایش دکمه ادامه با انیمیشن
                const continueButton = document.getElementById('continueButton');
                continueButton.style.display = 'block';
                continueButton.style.opacity = '0';
                continueButton.style.transform = 'translateY(20px)';

                setTimeout(() => {
                    continueButton.style.transition = 'all 0.5s ease';
                    continueButton.style.opacity = '1';
                    continueButton.style.transform = 'translateY(0)';
                }, 100);
            }

            // شروع فرآیند
            updateProgress();
            activateStep(0);
        });
    </script>
@endsection
