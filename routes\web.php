<?php

use App\Http\Controllers\Admin\AdminBlogController;
use App\Http\Controllers\Admin\AdminConsultController;
use App\Http\Controllers\Admin\AdminDietController;
use App\Http\Controllers\Admin\AdminFinanceController;
use App\Http\Controllers\Admin\AdminHomeController;
use App\Http\Controllers\Admin\AdminSettingController;
use App\Http\Controllers\Admin\AdminTicketController;
use App\Http\Controllers\Admin\AdminUserController;
use App\Http\Controllers\Admin\GalleryController;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\BlogController;
use App\Http\Controllers\PaymentController;
use App\Http\Controllers\SupportController;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\ProfileController;

Route::get('/', [HomeController::class, 'index'])->name('home.index');

Route::prefix('panel')->middleware('admin')->name('panel.')->group(function () {
    Route::get('/', [AdminHomeController::class, 'index'])->name('dashboard');

    Route::get('/diets', [AdminDietController::class, 'index'])->name('diets.index');
    Route::get('/diets/edit/{id}', [AdminDietController::class, 'edit'])->name('diets.edit');
    Route::put('/diets/{id}', [AdminDietController::class, 'update'])->name('diets.update');

    Route::get('/finance', [AdminFinanceController::class, 'index'])->name('finance.index');

    Route::prefix('users')->name('users.')->group(function () {
        Route::get('/', [AdminUserController::class, 'index'])->name('index');
        Route::get('/export', [AdminUserController::class, 'export'])->name('export');
        Route::get('/{id}/toggle-admin', [AdminUserController::class, 'toggleAdmin'])->name('toggle-admin');
        Route::get('/{user}', [AdminUserController::class, 'show'])->name('show');
        Route::post('/{user}/set-diet', [AdminUserController::class, 'setDiet'])->name('set-diet');
        Route::post('/{user}/doctor-comment', [AdminUserController::class, 'storeDoctorComment'])
            ->name('doctor-comment.store');
        Route::delete('/doctor-comment/{comment}', [AdminUserController::class, 'destroyDoctorComment'])
            ->name('doctor-comment.destroy');

        Route::get('/{user}/weights/chart', [AdminUserController::class, 'weightChartData'])->name('weights.chart');


    });

    Route::prefix('/blogs')->name('blogs.')->group(function () {

        // مقالات
        Route::get('/', [AdminBlogController::class, 'index'])->name('index');
        Route::get('/create', [AdminBlogController::class, 'create'])->name('create');
        Route::post('/', [AdminBlogController::class, 'store'])->name('store');
        Route::get('/{id}/edit', [AdminBlogController::class, 'edit'])->name('edit');
        Route::put('/{id}', [AdminBlogController::class, 'update'])->name('update');
        Route::delete('/{id}', [AdminBlogController::class, 'destroy'])->name('destroy');

        // دسته‌بندی‌ها
        Route::get('/categories', [AdminBlogController::class, 'categories'])->name('categories');
        Route::post('/categories', [AdminBlogController::class, 'storeCategory'])->name('categories.store');
        Route::put('/categories/{id}', [AdminBlogController::class, 'updateCategory'])->name('categories.update');
        Route::delete('/categories/{id}', [AdminBlogController::class, 'destroyCategory'])->name('categories.destroy');
    });


    Route::get('/settings', [AdminSettingController::class, 'index'])->name('settings');
    Route::post('/settings', [AdminSettingController::class, 'update'])->name('settings.update');

    Route::get('/consults', [AdminConsultController::class, 'index'])->name('consults');
    Route::get('/consults/followed', [AdminConsultController::class, 'followed'])
        ->name('consults.followed');
    Route::post('/consults/{id}/follow', [AdminConsultController::class, 'markAsFollowed'])
        ->name('consults.follow');


    Route::prefix('/tickets')->name('tickets.')->group(function () {
        Route::get('/', [AdminTicketController::class, 'index'])->name('index');
        Route::get('/create', [AdminTicketController::class, 'create'])->name('create');
        Route::post('/store', [AdminTicketController::class, 'store'])->name('store');
        Route::get('/{id}', [AdminTicketController::class, 'show'])->name('show');
        Route::post('/{id}/reply', [AdminTicketController::class, 'reply'])->name('reply');
        Route::post('/{id}/status', [AdminTicketController::class, 'changeStatus'])->name('status');
        Route::patch('/{ticket}/close', [AdminTicketController::class, 'close'])->name('close');
    });

    // Gallery routes
    Route::resource('galleries', GalleryController::class);

});

Route::prefix('home')->controller(HomeController::class)->group(function () {
    Route::get('/', 'index')->name('home.index'); // URL: /home
    Route::get('/results', 'results')->name('home.results'); // URL: /home
    Route::get('/about-us', 'aboutUs')->name('home.about-us'); // URL: /home/<USER>
    Route::get('/contact-us', 'contactUs')->name('home.contact-us'); // URL: /home/<USER>
    Route::get('/roadmap', 'roadmap')->name('home.roadmap'); // URL: /home/<USER>
    Route::get('/faq', 'faq')->name('home.faq'); // URL: /home/<USER>
});

Route::get('/privacy-policy', function () {
    return view('privacy-policy');
})->name('privacy');

Route::get('/terms-of-service', function () {
    return view('terms-of-service');
})->name('terms');

Route::get('/blog/{slug}', [BlogController::class, 'show'])->name('blog.show');
Route::get('/blogs', [BlogController::class, 'index'])->name('blogs.index');


Route::middleware(['auth'])->prefix('profile')->controller(ProfileController::class)->group(function () {
    Route::get('/', 'index')->name('profile.index');
    Route::get('/my-diet', 'mydiet')->name('profile.my-diet');
    Route::get('/liked-blogs', 'likedBlogs')->name('profile.liked-blogs');
    Route::get('/finance', 'finance')->name('profile.finance');
    Route::get('/setting', 'setting')->name('profile.setting');
    Route::put('/setting', [ProfileController::class, 'updateSetting'])->name('profile.setting.update');
    Route::get('/get-diet', 'get-diet')->name('profile.get-diet');
    Route::get('/predata', 'predata')->name('profile.predata');
    Route::post('/data-store', 'storeUserDetails')->name('profile.datastore');
    Route::get('/creating-diet', 'creatingDiet')->name('profile.creating-diet');
    Route::get('/support', 'support')->name('profile.support');
    Route::get('/new-ticket', 'newTicket')->name('profile.new-ticket');
    Route::post('/submit-ticket', 'submitTicket')->name('profile.submit-ticket');
    Route::get('/ticket/{id}', 'ticket')->name('profile.ticket');
    Route::post('/ticket/{id}/reply', 'submitTicketReply')->name('profile.ticket.reply');
    Route::post('/weight', 'storeWeight')->name('profile.weight.store');
    Route::get('/weight-chart/data', 'weightChartData')->name('weight.chart.data');
    Route::get('/next-stage', 'nextStage')->name('profile.next-stage');
});

Route::get('/payment/pay', [PaymentController::class, 'pay'])->name('payment.pay');
Route::get('/payment/callback', [PaymentController::class, 'payCallback'])->name('payment.callback');

Route::prefix('support')->controller(SupportController::class)->group(function () {
    Route::get('/', 'index')->name('support.index'); // URL: /profile
});

Route::middleware('guest')->group(function () {
    Route::get('/signin', [AuthController::class, 'showSignIn'])->name('signin');
    Route::get('/login', [AuthController::class, 'showSignIn'])->name('login');
    Route::post('/sendsms', [AuthController::class, 'sendSms'])->name('sendsms');
});
Route::get('/verify', [AuthController::class, 'showVerify'])->name('verify');
Route::post('/verifycode', [AuthController::class, 'verifyCode'])->name('verifycode');

Route::get('/logout', function () {
    session()->flush(); // حذف همه سشن‌ها
    return redirect('/signin')->with('success', 'با موفقیت خارج شدید.');
})->name('logout');

Route::post('/consultation-request', [HomeController::class, 'consultationStore'])->name('consultation.store');

Route::get('/logout-and-delete-user', function () {
    $user = auth()->user();

    if ($user) {
        // حذف متاها (اگه روابط cascade نداری)
        \App\Models\UserMeta::where('user_id', $user->id)->delete();

        // خروج از حساب
        auth()->logout();

        // حذف یوزر از دیتابیس
        $user->delete();

        // هدایت به صفحه ورود
        return redirect()->route('login')->with('success', 'اکانت شما با موفقیت حذف شد.');
    }

    return redirect()->route('login');
})->name('logout.delete.user');


Route::get('/aparat-video1', function () {
    return view('aparat.video1');
})->name('aparat.video1');

Route::get('/aparat-video2', function () {
    return view('aparat.video2');
})->name('aparat.video2');



