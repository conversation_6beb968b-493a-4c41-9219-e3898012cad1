<?php

namespace App\Http\Controllers;

use App\Models\Blog;
use App\Models\DietMeta;
use App\Models\DietPlan;
use App\Models\Payment;
use App\Models\Ticket;
use App\Models\TicketMeta;
use App\Models\User;
use App\Models\UserMeta;
use App\Traits\ConvertsPersianDigits;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Morilog\Jalali\Jalalian;


class ProfileController extends Controller
{
    use ConvertsPersianDigits;
    public function index()
    {
        $user = auth()->user();

        // محاسبه روزهای باقی‌مانده رژیم فعلی
        $daysRemaining = $user->diet_start_at && $user->diet_duration_days
            ? max(0, $user->diet_duration_days - Carbon::parse($user->diet_start_at)->diffInDays(now()))
            : 0;

        // محاسبه روزهای شروع رژیم
        $firstDietStart = UserMeta::where('user_id', $user->id)
            ->where('key', 'first_diet_start')
            ->value('value');

        $daysSinceFirstDiet = $firstDietStart
            ? Carbon::parse($firstDietStart)->diffInDays(Carbon::now())
            : 0;

        // محاسبه وزن کاهش یافته
        $startWeight = UserMeta::where('user_id', $user->id)
            ->where('key', 'first_diet_start')
            ->value('value')
            ? UserMeta::where('user_id', $user->id)
                ->where('key', 'weight_' . Carbon::parse($firstDietStart)->format('Y-m-d'))
                ->value('value')
            : $user->weight;

        $lastWeight = UserMeta::where('user_id', $user->id)
            ->where('key', 'like', 'weight_%')
            ->orderBy('created_at', 'desc')
            ->value('value');

        $weightLost = $lastWeight ? max(0, $startWeight - $lastWeight) : 0;

        return view('profile.index', compact('daysRemaining', 'daysSinceFirstDiet', 'weightLost'));
    }


    public function predata()
    {
        $user = auth()->user();
        $dietPlan = $user->activeDietPlan;

        if (!$dietPlan) {
            return view('profile.predata');
        } else {
            return redirect()->route('profile.my-diet');
        }
    }

    public function storeUserDetails(Request $request)
    {
        // تبدیل اعداد فارسی به انگلیسی
        $this->convertPersianDigitsInRequest($request, ['height', 'weight']);

        // اعتبارسنجی
        $validatedData = $request->validate([
            'name' => 'required|string|max:255',
            'height' => 'required|numeric',
            'weight' => 'required|numeric',
            'blood_group' => 'required|string',
            'gender' => 'required|string',
            'body_type' => 'required|string',
            'diabetes' => 'nullable|boolean',
            'thyroid' => 'nullable|boolean',
        ]);

        $user = auth()->user();

        // بروزرسانی اطلاعات کاربر
        $user->update([
            'name' => $validatedData['name'],
            'height' => $validatedData['height'],
            'weight' => $validatedData['weight'],
            'blood_group' => $validatedData['blood_group'],
            'gender' => $validatedData['gender'],
            'body_type' => $validatedData['body_type'],
            'diabetes' => $request->input('diabetes', false),
            'thyroid' => $request->input('thyroid', false),
            'diet_stage' => 10,
        ]);

        // جستجوی رژیم مناسب
        $bloodGroup = strtoupper(str_replace(['+', '-'], '', $user->blood_group));

        $dietPlan = DietPlan::where('week_number', 1)
            ->where('body_type', $user->body_type)
            ->where(function ($query) use ($bloodGroup) {
                $query->where('blood_group', $bloodGroup)
                    ->orWhere('blood_group', 'all');
            })
            ->first();

        if (!$dietPlan) {
            return redirect()->back()->with('error', 'رژیم مناسب پیدا نشد.');
        }

        // تنظیم رژیم فعال
        $user->update([
            'active_diet_id' => $dietPlan->id,
            'diet_start_at' => now(),
            'diet_duration_days' => 14,
        ]);

        // فقط برای اولین بار رژیم
        UserMeta::updateOrCreate([
            'user_id' => $user->id,
            'key' => 'first_diet_start'
        ], [
            'value' => now(),
            'notes' => 'شروع اولین رژیم'
        ]);

        return redirect()->route('profile.creating-diet');
    }

    public function creatingDiet()
    {
        $user = auth()->user();

        // اطمینان از اینکه کاربر اطلاعاتش را ثبت کرده
        if (!$user->height || !$user->weight || !$user->body_type) {
            return redirect()->route('profile.predata');
        }

        // چک کردن اینکه آیا کاربر قبلاً این صفحه را دیده یا نه
        $hasSeenCreatingPage = UserMeta::where('user_id', $user->id)
            ->where('key', 'has_seen_creating_diet_page')
            ->exists();

        if ($hasSeenCreatingPage) {
            // اگر قبلاً دیده، مستقیماً به صفحه رژیم هدایت کن
            return redirect()->route('profile.my-diet');
        }

        // ثبت اینکه کاربر این صفحه را دیده
        UserMeta::create([
            'user_id' => $user->id,
            'key' => 'has_seen_creating_diet_page',
            'value' => now(),
            'notes' => 'کاربر صفحه ساخت رژیم را دیده است'
        ]);

        return view('profile.creating-diet');
    }

    public function mydiet()
    {
        $user = Auth::user();
        $dietPlan = $user->activeDietPlan;

        if (!$dietPlan) {
            return redirect('profile/predata');
        }

        // گرفتن وزن شروع
        $startDate = Carbon::parse($user->diet_start_at)->format('Y-m-d');
        $startWeightMeta = UserMeta::where('user_id', $user->id)
            ->where('key', 'weight_' . $startDate)
            ->first();

        $startWeight = $startWeightMeta ? (float)$startWeightMeta->value : null;

        // محاسبه وزن هدف
        $weightGoal = null;
        if ($startWeight && $user->gender && $dietPlan) {
            $expectedPercent = $user->gender === 'male'
                ? $dietPlan->expected_loss_male
                : $dietPlan->expected_loss_female;

            $weightGoal = round($startWeight - (($expectedPercent / 100) * $startWeight), 1);
        }

        $dietMetas = DietMeta::where('diet_plan_id', $dietPlan->id)->get();

        $weeklyPlan = [];
        foreach ($dietMetas as $meta) {
            $parts = explode('_', $meta->key);
            if (count($parts) === 2) {
                $day = $parts[0];
                $meal = $parts[1];
                $weeklyPlan[$day][$meal] = $meta->value;
            }
        }

        // داده‌های وزن
        $twoWeeksAgo = Carbon::now()->subDays(14)->format('Y-m-d');
        $weights = UserMeta::where('user_id', $user->id)
            ->where('key', 'like', 'weight_%')
            ->where('key', '>=', 'weight_' . $twoWeeksAgo)
            ->orderBy('key', 'asc')
            ->get();

        $chartData = $weights->map(function ($item) {
            $date = str_replace('weight_', '', $item->key);
            return [
                'date' => $date,
                'weight' => (float)$item->value,
            ];
        });

        // وزن ابتدای دوره
        $startingWeight = $user->weight;

        // وزن فعلی (آخرین وزن ثبت‌شده در متا)
        $lastWeight = $weights->last()?->value;

        // وضعیت: در حال پیشرفت یا توقف یا عدم ثبت
        if ($lastWeight) {
            if ($lastWeight < $startingWeight) {
                $status = 'در حال پیشرفت';
            } elseif ($lastWeight > $startingWeight) {
                $status = 'افزایش وزن';
            } else {
                $status = 'بدون تغییر';
            }
        } else {
            $status = 'وزنی ثبت نشده';
        }

        // تبدیل تاریخ‌ها به شمسی
        $startDate = $user->diet_start_at
            ? Jalalian::fromCarbon($user->diet_start_at)->format('%d / %m / %Y')
            : '---';

        $endDate = ($user->diet_start_at && $user->diet_duration_days)
            ? Jalalian::fromCarbon($user->diet_start_at->copy()->addDays($user->diet_duration_days))->format('%d / %m / %Y')
            : '---';

        $doctorComments = \App\Models\DoctorComment::where('user_id', $user->id)
            ->where('diet_plan_id', $dietPlan->id)
            ->orderBy('created_at', 'desc')
            ->get();

        $relatedBlogs = Blog::where('diet_plan_id', $dietPlan->id)->latest()->take(3)->get();


        return view('profile.mydiet', compact(
            'dietPlan',
            'weeklyPlan',
            'chartData',
            'startDate',
            'endDate',
            'startingWeight',
            'status',
            'doctorComments',
            'weightGoal',
            'relatedBlogs',
        ));
    }

    public function likedBlogs(Request $request)
    {
        $user = auth()->user();

        // دریافت شناسه مقالات لایک‌شده از UserMeta
        $likedBlogIds = UserMeta::where('user_id', $user->id)
            ->where('key', 'liked_blog')
            ->pluck('value')
            ->toArray();

        // دریافت مقالات بر اساس شناسه‌های ذخیره‌شده
        $blogs = Blog::whereIn('id', $likedBlogIds)
            ->orderBy('created_at', 'desc')
            ->paginate(6);

        return view('profile.likedblogs', compact('blogs'));
    }

    public function finance()
    {
        $user = auth()->user();

        $payments = Payment::where('user_id', $user->id)
            ->orderBy('id', 'desc')
            ->paginate(10);

        return view('profile.finance', compact('payments'));
    }

    public function setting()
    {
        return view('profile.setting');
    }

    public function updateSetting(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255|unique:users,email,' . auth()->id(),
        ]);

        $user = auth()->user();
        $user->update([
            'name' => $request->name,
            'email' => $request->email,
        ]);

        return back()->with('success', 'اطلاعات شما با موفقیت به‌روزرسانی شد.');
    }


    public function support()
    {
        $user = auth()->user();

        $tickets = Ticket::where('user_id', $user->id)
            ->orderBy('id', 'desc')
            ->paginate(10);

        return view('profile.support', compact('tickets'));
    }

    public function newTicket()
    {
        return view('profile.new-ticket');
    }

    public function submitTicket(Request $request)
    {
        $validatedData = $request->validate([
            'title' => 'required|string|max:255',
            'subject' => 'required|string|max:255',
            'message' => 'required|string',
        ]);

        // ایجاد تیکت
        $ticket = Ticket::create([
            'user_id' => auth()->id(),
            'title' => $validatedData['title'],
            'subject' => $validatedData['subject'],
            'status' => 'open',
        ]);

        // ایجاد پیام اول تیکت در TicketMeta
        TicketMeta::create([
            'ticket_id' => $ticket->id,
            'user_id' => auth()->id(),
            'message' => $validatedData['message'],
        ]);

        // ریدایرکت به صفحه لیست تیکت‌ها یا نمایش تیکت
        return redirect()->route('profile.support')->with('success', 'تیکت شما با موفقیت ثبت شد!');
    }

    public function ticket($id)
    {
        // دریافت تیکت به همراه متاها و اطلاعات کاربر ارسال‌کننده هر متا
        $ticket = Ticket::with('metas.user')->findOrFail($id);

        // اگر می‌خواهید فقط صاحب تیکت یا ادمین بتواند تیکت را ببیند، اینجا می‌توانید بررسی کنید
        if (auth()->id() !== $ticket->user_id && auth()->user()->role !== 'admin') {
            abort(403, 'شما اجازه دسترسی به این تیکت را ندارید.');
        }

        return view('profile.ticket', compact('ticket'));
    }

    public function submitTicketReply(Request $request, $id)
    {
        $request->validate([
            'message' => 'required|string',
        ]);

        $ticket = Ticket::findOrFail($id);

        // ایجاد یک متا جدید (پاسخ تیکت)
        TicketMeta::create([
            'ticket_id' => $ticket->id,
            'user_id' => auth()->id(),
            'message' => $request->message,
        ]);

        // در صورت نیاز می‌توانید وضعیت تیکت را تغییر دهید:
        $ticket->update(['status' => 'pending']);

        return redirect()->route('profile.ticket', $ticket->id)
            ->with('success', 'پاسخ شما ثبت شد.');
    }

    public function storeWeight(Request $request)
    {
        // تبدیل اعداد فارسی به انگلیسی
        $this->convertPersianDigitsInRequest($request, ['weight']);

        $request->validate([
            'weight' => 'required|numeric|min:1|max:500',
        ]);

        $user = auth()->user();
        // کلید مخصوص به تاریخ امروز
        $todayKey = 'weight_' . Carbon::now()->format('Y-m-d');

        // اگر برای امروز ثبت شده باشد، جلوگیری می‌کنیم
        $exists = UserMeta::where('user_id', $user->id)
            ->where('key', $todayKey)
            ->exists();

        if ($exists) {
            return back()->with('error', 'شما امروز وزن خود را ثبت کرده‌اید.');
        }

        // ثبت وزن امروز در UserMeta
        UserMeta::create([
            'user_id' => $user->id,
            'key' => $todayKey,
            'value' => $request->weight,
        ]);

        return back()->with('success', 'وزن شما با موفقیت ثبت شد.');
    }

    public function weightChartData()
    {
        $user = auth()->user();

        // واکشی رکوردهای وزن کاربر که کلیدشان به شکل weight_YYYY-MM-DD است
        $weights = UserMeta::where('user_id', $user->id)
            ->where('key', 'like', 'weight_%')
            ->get();

        // ساخت یک آرایه [ 'YYYY-MM-DD' => وزن ]
        $weightMap = [];
        foreach ($weights as $item) {
            $date = str_replace('weight_', '', $item->key); // استخراج تاریخ از key
            $weightMap[$date] = (float)$item->value;
        }

        // اگر هیچ داده‌ای نباشد، خالی برگردیم
        if (empty($weightMap)) {
            return response()->json([]);
        }

        // پیدا کردن اولین و آخرین تاریخ
        $dates = array_keys($weightMap);
        sort($dates); // مرتب‌سازی
        $minDate = Carbon::parse($dates[0])->subDays(1);  // 3 روز قبل از اولین تاریخ
        $maxDate = Carbon::parse($dates[count($dates) - 1])->addDays(1); // 3 روز بعد از آخرین تاریخ

        // ساخت آرایه از همه تاریخ‌ها بین minDate و maxDate
        $chartData = [];
        $current = $minDate->copy();
        while ($current->lte($maxDate)) {
            $d = $current->format('Y-m-d');
            $dj = Jalalian::fromCarbon(Carbon::parse($current))->format('%m/%d');
            $chartData[] = [
                'date' => $dj,
                'weight' => $weightMap[$d] ?? null, // اگر وزنی ثبت نشده باشد null می‌گذاریم
            ];
            $current->addDay();
        }

        // برگرداندن داده در قالب JSON یا ارسال به ویو
        return response()->json($chartData);
    }


    public function nextStage()
    {
        $user = auth()->user();
        $todayKey = 'weight_' . Carbon::now()->format('Y-m-d');

        // چک وزن امروز
        $hasTodayWeight = UserMeta::where('user_id', $user->id)
            ->where('key', $todayKey)
            ->exists();

        if (!$hasTodayWeight) {
            return redirect()->route('profile.my-diet')->with('error', 'لطفاً ابتدا وزن امروز خود را ثبت کنید.');
        }

        $currentDiet = $user->activeDietPlan;

        if (!$currentDiet) {
            return back()->with('error', 'رژیم فعلی شما یافت نشد.');
        }

        if (now()->lessThan($user->diet_start_at->addDays($user->diet_duration_days))) {
            return back()->with('error', 'هنوز این مرحله از رژیم تمام نشده است.');
        }

        $startDate = Carbon::parse($user->diet_start_at)->format('Y-m-d');

        // 1. اول دنبال وزن در روز شروع یا قبلش
        $startWeightMeta = UserMeta::where('user_id', $user->id)
            ->where('key', 'like', 'weight_%')
            ->whereRaw("SUBSTRING(`key`, 8) <= ?", [$startDate])
            ->orderByDesc('key')
            ->first();

        // 2. اگر پیدا نشد، دنبال نزدیک‌ترین بعد از شروع رژیم
        if (!$startWeightMeta) {
            $startWeightMeta = UserMeta::where('user_id', $user->id)
                ->where('key', 'like', 'weight_%')
                ->whereRaw("SUBSTRING(`key`, 8) >= ?", [$startDate])
                ->orderBy('key')
                ->first();
        }

        // وزن فعلی
        $todayWeightMeta = UserMeta::where('user_id', $user->id)
            ->where('key', $todayKey)
            ->first();

        if (!$startWeightMeta || !$todayWeightMeta) {
            return back()->with('error', 'وزن‌های لازم برای مقایسه یافت نشدند.');
        }

        $startWeight = floatval($startWeightMeta->value);
        $currentWeight = floatval($todayWeightMeta->value);

        // محاسبه درصد کاهش وزن
        $weightLossPercent = (($startWeight - $currentWeight) / $startWeight) * 100;

        // درصد مورد انتظار بر اساس جنسیت
        $expectedPercent = $user->gender === 'female'
            ? $currentDiet->expected_loss_female
            : $currentDiet->expected_loss_male;

        // اگر درصد کاهش وزن کافی بود → مرحله بعد
        if ($weightLossPercent >= $expectedPercent) {
            $nextDiet = DietPlan::where(function ($query) use ($user) {
                $query->where('body_type', $user->body_type)
                    ->orWhere('body_type', 'all');
            })
                ->where(function ($query) use ($user) {
                    $bloodGroup = preg_replace('/[^A-Z]/', '', $user->blood_group);
                    $query->where('blood_group', $bloodGroup)
                        ->orWhere('blood_group', 'all');
                })
                ->where('week_number', $currentDiet->week_number + 1)
                ->first();


            if (!$nextDiet) {
                return back()->with('error', 'رژیم جدید برای شما یافت نشد.');
            }

            $user->update([
                'active_diet_id' => $nextDiet->id,
                'diet_stage' => $nextDiet->week_number * 10,
                'diet_start_at' => now(),
                'diet_duration_days' => 14 * $nextDiet->week_number,
                'current_diet_paid' => false,
            ]);

            UserMeta::create([
                'user_id' => $user->id,
                'key' => 'stage_changed_at_' . $nextDiet->week_number,
                'value' => now(),
            ]);

            return redirect()->route('profile.my-diet')->with('success', 'رژیم جدید شما تنظیم شد!');
        }

        // درصد کافی نبوده → تنظیم رژیم مرحله اول
        $bloodGroup = preg_replace('/[^A-Z]/', '', $user->blood_group);
        $fallbackDiet = DietPlan::where('body_type', $user->body_type)
            ->where(function ($query) use ($bloodGroup) {
                $query->where('blood_group', $bloodGroup)->orWhere('blood_group', 'all');
            })
            ->where('week_number', 1)
            ->first();

        if (!$fallbackDiet) {
            return back()->with('error', 'رژیم مرحله اول یافت نشد.');
        }

        // محاسبه تعداد روزها (بر اساس درصد)
        $adjustedDays = $weightLossPercent > 0
            ? ceil(($weightLossPercent / $expectedPercent) * 14)
            : 14;

        if ($adjustedDays < 7) {
            $adjustedDays = 7;
        }

        $user->update([
            'active_diet_id' => $fallbackDiet->id,
            'diet_stage' => 1 * 14,
            'diet_start_at' => now(),
            'diet_duration_days' => $adjustedDays,
            'current_diet_paid' => false,
        ]);

        UserMeta::create([
            'user_id' => $user->id,
            'key' => 'stage_reset_at_' . now()->format('Y_m_d'),
            'value' => now(),
            'notes' => 'ریست مرحله به دلیل عدم تحقق درصد کاهش وزن',
        ]);

        return redirect()->route('profile.my-diet')
            ->with('success', 'رژیم شما به مدت ' . $adjustedDays . ' روز دوباره بر اساس مرحله اول تنظیم شد.

    این تصمیم صرفاً برای این گرفته شده که بدن شما فرصت بیشتری برای سازگاری با روند رژیم پیدا کند.
    گاهی اوقات، کاهش وزن در هفته‌های ابتدایی به دلایل مختلفی مانند شرایط بدنی، سبک زندگی یا تغییرات هورمونی کندتر از حد انتظار پیش می‌رود.
    نگران نباشید! این موضوع طبیعی است و هدف ما این است که با صبوری و استمرار، بهترین نتیجه را برای سلامتی و تناسب اندام شما فراهم کنیم.

    رژیم فعلی به گونه‌ای طراحی شده که متناسب با شرایط فعلی شما، مجدداً بدن را به روند چربی‌سوزی و کاهش وزن بازگرداند.
    به مسیر خود ادامه دهید، ما همراه شما هستیم 🌱💪');
    }


}

